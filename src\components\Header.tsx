import React, { useState } from 'react'
import { Link } from 'react-router-dom'
import ApiStatus from './ApiStatus'

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isSearchOpen, setIsSearchOpen] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen)
  }

  const toggleSearch = () => {
    setIsSearchOpen(!isSearchOpen)
  }

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    if (searchQuery.trim()) {
      // Navigate to search page
      window.location.href = `/search?q=${encodeURIComponent(searchQuery)}`
    }
  }

  return (
    <header className="bg-gray-900 border-b border-gray-800">
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link to="/" className="flex items-center">
            <span className="text-red-600 font-bold text-2xl mr-1">Stream</span>
            <span className="text-white font-bold text-2xl">SB</span>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-6">
            <Link to="/" className="text-gray-300 hover:text-white transition">Trang chủ</Link>
            <Link to="/phim-le" className="text-gray-300 hover:text-white transition">Phim lẻ</Link>
            <Link to="/phim-bo" className="text-gray-300 hover:text-white transition">Phim bộ</Link>
            <Link to="/episode-selector" className="text-gray-300 hover:text-white transition">Episode Selector</Link>
            <div className="relative group">
              <button className="text-gray-300 hover:text-white transition flex items-center">
                Thể loại
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
              <div className="absolute left-0 mt-2 w-48 bg-gray-800 rounded-md shadow-lg py-1 z-50 hidden group-hover:block">
                <Link to="/categories" className="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white font-semibold border-b border-gray-700">Tất cả thể loại</Link>
                <Link to="/the-loai/hanh-dong" className="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white">Hành động</Link>
                <Link to="/the-loai/tinh-cam" className="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white">Tình cảm</Link>
                <Link to="/the-loai/hai-huoc" className="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white">Hài hước</Link>
                <Link to="/the-loai/co-trang" className="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white">Cổ trang</Link>
                <Link to="/the-loai/tam-ly" className="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white">Tâm lý</Link>
                <Link to="/the-loai/hinh-su" className="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white">Hình sự</Link>
              </div>
            </div>
          </nav>

          {/* Search and Menu Icons */}
          <div className="flex items-center space-x-4">
            <button onClick={toggleSearch} className="text-gray-300 hover:text-white transition">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </button>
            <button onClick={toggleMenu} className="md:hidden text-gray-300 hover:text-white transition">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="md:hidden py-4 border-t border-gray-800">
            <nav className="flex flex-col space-y-3">
              <Link to="/" className="text-gray-300 hover:text-white transition">Trang chủ</Link>
              <Link to="/phim-le" className="text-gray-300 hover:text-white transition">Phim lẻ</Link>
              <Link to="/phim-bo" className="text-gray-300 hover:text-white transition">Phim bộ</Link>
              <Link to="/episode-selector" className="text-gray-300 hover:text-white transition">Episode Selector</Link>
              <div className="relative">
                <button className="text-gray-300 hover:text-white transition flex items-center">
                  Thể loại
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </button>
                <div className="pl-4 mt-2 space-y-2">
                  <Link to="/categories" className="block text-sm text-gray-300 hover:text-white font-semibold">Tất cả thể loại</Link>
                  <Link to="/the-loai/hanh-dong" className="block text-sm text-gray-400 hover:text-white">Hành động</Link>
                  <Link to="/the-loai/tinh-cam" className="block text-sm text-gray-400 hover:text-white">Tình cảm</Link>
                  <Link to="/the-loai/hai-huoc" className="block text-sm text-gray-400 hover:text-white">Hài hước</Link>
                  <Link to="/the-loai/co-trang" className="block text-sm text-gray-400 hover:text-white">Cổ trang</Link>
                  <Link to="/the-loai/tam-ly" className="block text-sm text-gray-400 hover:text-white">Tâm lý</Link>
                  <Link to="/the-loai/hinh-su" className="block text-sm text-gray-400 hover:text-white">Hình sự</Link>
                </div>
              </div>
            </nav>
          </div>
        )}

        {/* Search Bar */}
        {isSearchOpen && (
          <div className="py-4 border-t border-gray-800">
            <form onSubmit={handleSearch} className="flex">
              <input
                type="text"
                placeholder="Tìm kiếm phim..."
                className="flex-1 bg-gray-800 text-white px-4 py-2 rounded-l focus:outline-none"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
              <button
                type="submit"
                className="bg-red-600 text-white px-4 py-2 rounded-r hover:bg-red-700 transition"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </button>
            </form>
          </div>
        )}
      </div>
    </header>
  )
}

export default Header
