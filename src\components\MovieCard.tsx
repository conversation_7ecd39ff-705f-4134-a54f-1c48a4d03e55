import React from 'react'
import { Link } from 'react-router-dom'
import type { Movie } from '../types/movie'
import kkPhimApi from '../services/api'

// Props cho MovieCard
interface MovieCardProps {
  movie: Movie
  className?: string
}

// Component hiển thị thẻ phim
const MovieCard: React.FC<MovieCardProps> = ({ movie, className = '' }) => {
  const [imageError, setImageError] = React.useState(false)
  
  // Xử lý lỗi khi không tải được ảnh
  const handleImageError = () => {
    setImageError(true)
  }
  
  return (
    <Link 
      to={`/movie/${movie.slug}`}
      className={`bg-gray-800 rounded-lg overflow-hidden hover:scale-105 transition duration-300 ${className}`}
    >
      <div className="aspect-[2/3] relative">
        <img 
          src={imageError ? '/images/poster-placeholder.jpg' : kkPhimApi.getImageUrl(movie.poster_url)}
          alt={movie.name}
          className="w-full h-full object-cover"
          loading="lazy"
          onError={handleImageError}
        />
        {movie.quality && (
          <div className="absolute top-2 right-2 bg-red-600 text-white text-xs px-2 py-1 rounded">
            {movie.quality}
          </div>
        )}
        {movie.episode_current && movie.type === 'series' && (
          <div className="absolute top-2 left-2 bg-blue-600 text-white text-xs px-2 py-1 rounded">
            Tập {movie.episode_current}
          </div>
        )}
      </div>
      <div className="p-2">
        <h3 className="text-white font-medium truncate">{movie.name}</h3>
        <div className="flex justify-between items-center">
          <p className="text-gray-400 text-sm">{movie.year}</p>
          {movie.tmdb?.vote_average > 0 && (
            <div className="flex items-center">
              <svg className="w-3 h-3 text-yellow-400 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
              </svg>
              <span className="text-xs text-gray-400">{movie.tmdb.vote_average.toFixed(1)}</span>
            </div>
          )}
        </div>
      </div>
    </Link>
  )
}

export default MovieCard
