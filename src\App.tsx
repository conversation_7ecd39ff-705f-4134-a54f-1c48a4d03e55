import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import React, { useState, useEffect, Suspense, lazy } from 'react'
import kkPhimApi from './services/api'
import MovieCard from './components/MovieCard'

// Lazy load components
const Header = lazy(() => import('./components/Header'))
const Hero = lazy(() => import('./components/Hero'))
const Footer = lazy(() => import('./components/Footer'))
const MovieDetail = lazy(() => import('./pages/MovieDetail'))

// ErrorBoundary component
class ErrorBoundary extends React.Component<{ children: React.ReactNode }> {
  state = { hasError: false, error: null }
  
  static getDerivedStateFromError(error: any) {
    return { hasError: true, error }
  }
  
  componentDidCatch(error: any, errorInfo: any) {
    console.error('Error caught by ErrorBoundary:', error, errorInfo)
  }
  
  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen bg-gray-900 text-white flex flex-col items-center justify-center p-4">
          <h1 className="text-3xl font-bold mb-4">Đã xảy ra lỗi</h1>
          <p className="mb-4">Vui lòng thử lại sau hoặc trở về trang chủ.</p>
          <button 
            onClick={() => window.location.href = '/'}
            className="px-4 py-2 bg-red-600 rounded hover:bg-red-700 transition"
          >
            Về trang chủ
          </button>
        </div>
      )
    }
    
    return this.props.children
  }
}

// LoadingIndicator component
const LoadingIndicator = () => (
  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
    {[...Array(10)].map((_, i) => (
      <div key={i} className="bg-gray-800 rounded-lg overflow-hidden animate-pulse">
        <div className="aspect-[2/3] bg-gray-700"></div>
        <div className="p-2">
          <div className="h-4 bg-gray-700 rounded mb-2"></div>
          <div className="h-3 bg-gray-700 rounded w-2/3"></div>
        </div>
      </div>
    ))}
  </div>
);

// ErrorDisplay component
const ErrorDisplay = ({ onRetry }: { onRetry: () => void }) => (
  <div className="text-center py-10">
    <div className="text-red-500 text-5xl mb-4">
      <i className="fas fa-exclamation-circle"></i>
    </div>
    <h3 className="text-xl font-semibold text-white mb-2">Không thể tải dữ liệu phim</h3>
    <p className="text-gray-400 mb-4">Vui lòng kiểm tra kết nối mạng và thử lại</p>
    <button 
      onClick={onRetry} 
      className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition"
    >
      Thử lại
    </button>
  </div>
);

// MovieSection component
const MovieSection = ({ title, type }: { title: string, type: string }) => {
  const [movies, setMovies] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(false)
  const [isEnabled, setIsEnabled] = useState(true)
  
  const fetchMovies = async () => {
    if (!isEnabled) return
    
    try {
      setLoading(true)
      setError(false)
      let response;
      
      switch (type) {
        case 'trending':
          response = await kkPhimApi.getNewMovies()
          break
        case 'popular':
          response = await kkPhimApi.getSeriesMovies()
          break
        case 'new-releases':
          response = await kkPhimApi.getNewMoviesV2()
          break
        case 'action':
          response = await kkPhimApi.getActionMovies()
          break
        case 'comedy':
          response = await kkPhimApi.getComedyMovies()
          break
        case 'tv-shows':
          // Đặc biệt xử lý cho TV Shows do gặp vấn đề CORS
          try {
            response = await kkPhimApi.getTVShows()
          } catch (tvShowsError) {
            console.error("TV Shows API is having issues, disabling section:", tvShowsError)
            setIsEnabled(false)
            return
          }
          break
        default:
          response = await kkPhimApi.getNewMovies()
      }
      
      if (response && response.data && response.data.items) {
        setMovies(response.data.items.slice(0, 10))
      } else {
        // Nếu response hợp lệ nhưng không có dữ liệu
        console.error(`No items found for ${type} movies`)
        setError(true)
      }
    } catch (error) {
      console.error(`Error fetching ${type} movies:`, error)
      
      // Đặc biệt xử lý cho TV Shows nếu gặp lỗi CORS
      if (type === 'tv-shows') {
        setIsEnabled(false)
        return
      }
      
      setError(true)
    } finally {
      setLoading(false)
    }
  }
  
  useEffect(() => {
    fetchMovies()
  }, [type])
  
  // Nếu section bị vô hiệu hóa thì không hiển thị
  if (!isEnabled) return null
  
  return (
    <section className="py-6">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-2xl font-bold text-white">{title}</h2>
        <a href="#" className="text-gray-400 hover:text-white transition">
          Xem tất cả &rarr;
        </a>
      </div>
      
      {loading ? (
        <LoadingIndicator />
      ) : error ? (
        <ErrorDisplay onRetry={fetchMovies} />
      ) : (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
          {movies.map((movie) => (
            <MovieCard key={movie._id} movie={movie} />
          ))}
        </div>
      )}
    </section>
  )
}

// TestPlayer component
const TestPlayer = () => {
  return (
    <div className="min-h-screen bg-gray-900 p-4">
      <h1 className="text-white text-2xl mb-4">Test Player</h1>
      <div className="aspect-video bg-black rounded-lg overflow-hidden">
        <video 
          controls
          className="w-full h-full"
          src="https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4"
        ></video>
      </div>
    </div>
  )
}

// HomePage component
function HomePage() {
  return (
    <div className="min-h-screen bg-gray-900">
      <Suspense fallback={<div className="text-white text-center p-8">Đang tải...</div>}>
        <Header />
        <Hero />
        <main className="px-4 md:px-8 lg:px-16 space-y-12 pb-16">
          <MovieSection title="Phim Đang Thịnh Hành" type="trending" />
          <MovieSection title="Phim Phổ Biến" type="popular" />
          <MovieSection title="Phim Mới Cập Nhật" type="new-releases" />
          <MovieSection title="Phim Hành Động" type="action" />
          <MovieSection title="Phim Hài Hước" type="comedy" />
          <MovieSection title="TV Shows" type="tv-shows" />
        </main>
        <Footer />
      </Suspense>
    </div>
  )
}

// App component
function App() {
  return (
    <ErrorBoundary>
      <Router>
        <Routes>
          <Route path="/" element={<HomePage />} />
          <Route path="/movie/:slug" element={
            <Suspense fallback={<div className="text-white text-center p-8">Đang tải...</div>}>
              <MovieDetail />
            </Suspense>
          } />
          <Route path="/test-player" element={<TestPlayer />} />
        </Routes>
      </Router>
    </ErrorBoundary>
  )
}

export default App
