import React, { useState } from 'react';
import { useMovieDetail } from '../hooks/useKKPhimApi';
import EpisodeSelector from './EpisodeSelector';
import type { MovieDetail } from '../types/api';

const EpisodeSelectorDemo: React.FC = () => {
  // Sử dụng một phim có nhiều tập để demo (Ngôi T<PERSON>ường Xác <PERSON>ng)
  const { movie, episodes, loading, error } = useMovieDetail('ngoi-truong-xac-song');
  const [currentEpisode, setCurrentEpisode] = useState<MovieDetail['episodes'][0]['server_data'][0] | null>(null);

  // Chọn tập đầu tiên khi có dữ liệu
  React.useEffect(() => {
    if (episodes.length > 0 && episodes[0].server_data.length > 0 && !currentEpisode) {
      setCurrentEpisode(episodes[0].server_data[0]);
    }
  }, [episodes, currentEpisode]);

  const handleEpisodeSelect = (episode: MovieDetail['episodes'][0]['server_data'][0]) => {
    setCurrentEpisode(episode);
  };

  if (loading) {
    return (
      <div className="bg-gray-800 rounded-lg p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-700 rounded w-48 mb-4"></div>
          <div className="space-y-3">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="h-16 bg-gray-700 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-gray-800 rounded-lg p-6">
        <div className="text-center">
          <div className="text-red-500 text-5xl mb-4">
            <svg className="w-12 h-12 mx-auto" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          </div>
          <h3 className="text-xl font-semibold text-white mb-2">Không thể tải dữ liệu phim</h3>
          <p className="text-gray-400">Lỗi: {error}</p>
        </div>
      </div>
    );
  }

  if (!movie || episodes.length === 0) {
    return (
      <div className="bg-gray-800 rounded-lg p-6">
        <div className="text-center">
          <div className="text-gray-500 text-5xl mb-4">
            <svg className="w-12 h-12 mx-auto" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
            </svg>
          </div>
          <h3 className="text-xl font-semibold text-white mb-2">Không có tập phim</h3>
          <p className="text-gray-400">Phim này chưa có tập phim nào</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Movie Info */}
      <div className="bg-gray-800 rounded-lg p-6">
        <div className="flex items-start space-x-4">
          <img 
            src={movie.poster_url} 
            alt={movie.name}
            className="w-24 h-36 object-cover rounded-lg"
          />
          <div className="flex-1">
            <h2 className="text-2xl font-bold text-white mb-2">{movie.name}</h2>
            <p className="text-gray-400 mb-2">{movie.origin_name}</p>
            <div className="flex flex-wrap gap-4 text-sm">
              <span className="text-gray-300">
                <strong>Năm:</strong> {movie.year}
              </span>
              <span className="text-gray-300">
                <strong>Tập:</strong> {movie.episode_current}/{movie.episode_total}
              </span>
              <span className="text-gray-300">
                <strong>Chất lượng:</strong> {movie.quality}
              </span>
              <span className="text-gray-300">
                <strong>Ngôn ngữ:</strong> {movie.lang}
              </span>
            </div>
            {movie.category.length > 0 && (
              <div className="mt-2">
                <span className="text-gray-400 text-sm">Thể loại: </span>
                {movie.category.map((cat, index) => (
                  <span key={cat.id} className="text-blue-400 text-sm">
                    {cat.name}{index < movie.category.length - 1 ? ', ' : ''}
                  </span>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Episode Selector */}
      <EpisodeSelector
        episodes={episodes}
        currentEpisode={currentEpisode || undefined}
        onEpisodeSelect={handleEpisodeSelect}
      />

      {/* Current Episode Player Info */}
      {currentEpisode && (
        <div className="bg-gray-800 rounded-lg p-6">
          <h3 className="text-white text-xl font-semibold mb-4">Thông tin phát</h3>
          <div className="grid md:grid-cols-2 gap-4">
            <div>
              <h4 className="text-gray-300 font-medium mb-2">Tập hiện tại</h4>
              <p className="text-white text-lg">{currentEpisode.name}</p>
            </div>
            <div>
              <h4 className="text-gray-300 font-medium mb-2">Chất lượng</h4>
              <span className="inline-block px-3 py-1 bg-red-600 text-white rounded text-sm">
                {currentEpisode.filename?.includes('1080p') ? 'Full HD' : 'HD'}
              </span>
            </div>
          </div>
          
          <div className="mt-4">
            <h4 className="text-gray-300 font-medium mb-2">URL Player</h4>
            <div className="bg-gray-900 p-3 rounded border">
              <code className="text-green-400 text-sm break-all">
                {(() => {
                  const playerUrl = currentEpisode.link_embed || currentEpisode.link_m3u8;
                  return playerUrl ? `https://player.phimapi.com/player/?url=${encodeURIComponent(playerUrl)}` : 'URL không khả dụng';
                })()}
              </code>
            </div>
          </div>

          <div className="mt-4 flex space-x-3">
            <button
              onClick={() => {
                const playerUrl = currentEpisode.link_embed || currentEpisode.link_m3u8;
                const displayUrl = playerUrl ? `https://player.phimapi.com/player/?url=${encodeURIComponent(playerUrl)}` : '';
                if (displayUrl) {
                  window.open(displayUrl, '_blank');
                }
              }}
              className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded transition"
            >
              Phát trong tab mới
            </button>
            <button
              onClick={() => {
                const playerUrl = currentEpisode.link_embed || currentEpisode.link_m3u8;
                const displayUrl = playerUrl ? `https://player.phimapi.com/player/?url=${encodeURIComponent(playerUrl)}` : '';
                if (displayUrl) {
                  navigator.clipboard.writeText(displayUrl);
                  alert('Đã copy URL vào clipboard!');
                }
              }}
              className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded transition"
            >
              Copy URL
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default EpisodeSelectorDemo;
