import React from 'react'
import type { MovieDetail } from '../types/api'

interface EpisodeSelectorProps {
  episodes: MovieDetail['episodes']
  currentEpisode?: MovieDetail['episodes'][0]['server_data'][0]
  onEpisodeSelect: (episode: MovieDetail['episodes'][0]['server_data'][0]) => void
  className?: string
}

const EpisodeSelector: React.FC<EpisodeSelectorProps> = ({
  episodes,
  currentEpisode,
  onEpisodeSelect,
  className = ''
}) => {
  if (!episodes || episodes.length === 0) {
    return null
  }

  return (
    <div className={`bg-gray-800 rounded-lg p-6 ${className}`}>
      <h3 className="text-white text-xl font-semibold mb-4">Danh sách tập phim</h3>

      {episodes.map((server, serverIndex) => (
        <div key={serverIndex} className="mb-6 last:mb-0">
          <h4 className="text-gray-300 text-lg font-medium mb-3 flex items-center">
            <svg className="w-5 h-5 mr-2 text-red-500" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z" clipRule="evenodd" />
            </svg>
            {server.server_name}
          </h4>

          <div className="space-y-2">
            {server.server_data.map((episode, episodeIndex) => {
              const isActive = currentEpisode?.slug === episode.slug
              const playerUrl = episode.link_embed || episode.link_m3u8
              const displayUrl = playerUrl ? `https://player.phimapi.com/player/?url=${encodeURIComponent(playerUrl)}` : 'URL không khả dụng'

              return (
                <div
                  key={episodeIndex}
                  className={`
                    p-3 rounded-md border transition-all duration-200 cursor-pointer
                    ${isActive
                      ? 'bg-red-600 border-red-500 text-white shadow-lg'
                      : 'bg-gray-700 border-gray-600 text-gray-300 hover:bg-gray-600 hover:border-gray-500 hover:text-white'
                    }
                  `}
                  onClick={() => onEpisodeSelect(episode)}
                  title={`Tập ${episode.name} - Click để phát`}
                >
                  <div className="flex flex-col space-y-1">
                    <div className="flex items-center justify-between">
                      <span className="font-medium text-sm">
                        Tập {episode.name}
                      </span>
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          navigator.clipboard.writeText(displayUrl)
                        }}
                        className="p-1 rounded hover:bg-gray-600 transition-colors"
                        title="Copy URL"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                        </svg>
                      </button>
                    </div>
                    <div className="text-xs text-gray-400 break-all">
                      {displayUrl}
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </div>
      ))}

      {/* Episode Info */}
      {currentEpisode && (
        <div className="mt-6 p-4 bg-gray-700 rounded-lg">
          <h5 className="text-white font-medium mb-3">Đang phát:</h5>

          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-gray-300 font-medium">
                Tập {currentEpisode.name}
              </span>
              <div className="flex items-center space-x-2 text-xs">
                <span className="text-gray-400">Chất lượng:</span>
                <span className="px-2 py-1 bg-red-600 text-white rounded text-xs">
                  {currentEpisode.filename?.includes('1080p') ? 'FHD' : 'HD'}
                </span>
              </div>
            </div>

            <div className="bg-gray-800 p-3 rounded border">
              <div className="flex items-center justify-between mb-2">
                <span className="text-gray-400 text-xs font-medium">URL Player:</span>
                <button
                  onClick={() => {
                    const playerUrl = currentEpisode.link_embed || currentEpisode.link_m3u8
                    const displayUrl = playerUrl ? `https://player.phimapi.com/player/?url=${encodeURIComponent(playerUrl)}` : ''
                    if (displayUrl) {
                      navigator.clipboard.writeText(displayUrl)
                    }
                  }}
                  className="p-1 rounded hover:bg-gray-600 transition-colors text-gray-400 hover:text-white"
                  title="Copy URL"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                  </svg>
                </button>
              </div>
              <div className="text-xs text-gray-300 break-all font-mono bg-gray-900 p-2 rounded">
                {(() => {
                  const playerUrl = currentEpisode.link_embed || currentEpisode.link_m3u8
                  return playerUrl ? `https://player.phimapi.com/player/?url=${encodeURIComponent(playerUrl)}` : 'URL không khả dụng'
                })()}
              </div>
            </div>

            {currentEpisode.filename && (
              <div className="text-gray-400 text-xs">
                <span className="font-medium">File:</span> {currentEpisode.filename}
              </div>
            )}

            <div className="text-xs text-gray-400 text-center pt-2 border-t border-gray-600">
              Chọn tập khác để xem tiếp
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default EpisodeSelector
