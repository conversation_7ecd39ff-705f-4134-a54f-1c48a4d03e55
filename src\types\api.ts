// Base API Response
export interface ApiResponse<T> {
  status: boolean;
  msg: string;
  data: T;
}

// Pagination
export interface PaginationInfo {
  seoOnPage: {
    og_type: string;
    titleHead: string;
    descriptionHead: string;
    og_image: string[];
    og_url: string;
  };
  breadCrumb: Array<{
    name: string;
    slug?: string;
    isCurrent?: boolean;
    position: number;
  }>;
  titlePage: string;
  currentPage: number;
  totalItems: number;
  totalItemsPerPage: number;
  totalPages: number;
}

// Movie Basic Info
export interface MovieBasic {
  modified: {
    time: string;
  };
  _id: string;
  name: string;
  slug: string;
  origin_name: string;
  content: string;
  type: string;
  status: string;
  poster_url: string;
  thumb_url: string;
  is_copyright: boolean;
  sub_docquyen: boolean;
  chieurap: boolean;
  trailer_url: string;
  time: string;
  episode_current: string;
  episode_total: string;
  quality: string;
  lang: string;
  notify: string;
  showtimes: string;
  year: number;
  view: number;
  actor: string[];
  director: string[];
  category: Array<{
    id: string;
    name: string;
    slug: string;
  }>;
  country: Array<{
    id: string;
    name: string;
    slug: string;
  }>;
}

// Movie Detail
export interface MovieDetail extends MovieBasic {
  episodes: Array<{
    server_name: string;
    server_data: Array<{
      name: string;
      slug: string;
      filename: string;
      link_embed: string;
      link_m3u8: string;
    }>;
  }>;
}

// Movie List Response
export interface MovieListResponse {
  items: MovieBasic[];
  params: {
    type_slug: string;
    filterCategory: string[];
    filterCountry: string[];
    filterYear: string;
    filterLang: string;
    sortField: string;
    sortType: string;
    pagination: {
      totalItems: number;
      totalItemsPerPage: number;
      currentPage: number;
      totalPages: number;
    };
  };
  type_list: string;
  APP_DOMAIN_FRONTEND: string;
  APP_DOMAIN_CDN_IMAGE: string;
  seoOnPage: PaginationInfo['seoOnPage'];
  breadCrumb: PaginationInfo['breadCrumb'];
  titlePage: string;
}

// Movie Detail Response
export interface MovieDetailResponse {
  movie: MovieDetail;
  episodes: MovieDetail['episodes'];
  seoOnPage: PaginationInfo['seoOnPage'];
  breadCrumb: PaginationInfo['breadCrumb'];
}

// Category/Country Item
export interface CategoryItem {
  _id: string;
  name: string;
  slug: string;
}

// Search Response
export interface SearchResponse extends MovieListResponse {
  // Same structure as MovieListResponse
}

// API Request Parameters
export interface MovieListParams {
  page?: number;
  sort_field?: string;
  sort_type?: string;
  sort_lang?: string;
  category?: string;
  country?: string;
  year?: number;
  limit?: number;
}

export interface SearchParams extends MovieListParams {
  keyword: string;
}
