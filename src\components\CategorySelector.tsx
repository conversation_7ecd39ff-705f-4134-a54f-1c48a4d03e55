import React, { useState } from 'react';
import { useCategories } from '../hooks/useKKPhimApi';
import CategoryMovieList from './CategoryMovieList';

const CategorySelector: React.FC = () => {
  const { categories, loading, error } = useCategories();
  const [selectedCategories, setSelectedCategories] = useState<string[]>([
    'hanh-dong',
    'hai-huoc', 
    'tinh-cam',
    'kinh-di',
    'vien-tuong',
    'co-trang'
  ]);

  if (loading) {
    return (
      <div className="py-6">
        <div className="h-8 bg-gray-800 rounded w-64 mb-4 animate-pulse"></div>
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-2">
          {[...Array(12)].map((_, i) => (
            <div key={i} className="h-8 bg-gray-800 rounded animate-pulse"></div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="py-6">
        <h2 className="text-2xl font-bold text-white mb-4">Thể Loại Phim</h2>
        <div className="text-center py-4">
          <p className="text-red-400">Không thể tải danh sách thể loại</p>
        </div>
      </div>
    );
  }

  const handleCategoryToggle = (categorySlug: string) => {
    setSelectedCategories(prev => {
      if (prev.includes(categorySlug)) {
        return prev.filter(slug => slug !== categorySlug);
      } else {
        return [...prev, categorySlug];
      }
    });
  };

  const getCategoryName = (slug: string) => {
    const category = categories.find(cat => cat.slug === slug);
    return category ? category.name : slug;
  };

  return (
    <div className="py-6">
      <h2 className="text-2xl font-bold text-white mb-4">Chọn Thể Loại Phim</h2>
      
      {/* Category selector */}
      <div className="mb-8">
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-2 mb-4">
          {categories.slice(0, 18).map((category) => (
            <button
              key={category._id}
              onClick={() => handleCategoryToggle(category.slug)}
              className={`px-3 py-2 rounded text-sm transition ${
                selectedCategories.includes(category.slug)
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              }`}
            >
              {category.name}
            </button>
          ))}
        </div>
        
        <div className="text-sm text-gray-400">
          Đã chọn {selectedCategories.length} thể loại. Click để thêm/bỏ thể loại.
        </div>
      </div>

      {/* Movie lists for selected categories */}
      <div className="space-y-12">
        {selectedCategories.map((categorySlug) => (
          <CategoryMovieList
            key={categorySlug}
            title={`Phim ${getCategoryName(categorySlug)}`}
            categorySlug={categorySlug}
            limit={10}
          />
        ))}
      </div>
    </div>
  );
};

export default CategorySelector;
