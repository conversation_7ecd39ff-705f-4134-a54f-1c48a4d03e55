import React from 'react';
import { useMoviesByCategory } from '../hooks/useKKPhimApi';
import MovieCard from './MovieCard';

interface CategoryMovieListProps {
  title: string;
  categorySlug: string;
  limit?: number;
  showViewAll?: boolean;
  className?: string;
}

const CategoryMovieList: React.FC<CategoryMovieListProps> = ({
  title,
  categorySlug,
  limit = 10,
  showViewAll = true,
  className = ''
}) => {
  const { movies, loading, error, refetch } = useMoviesByCategory(categorySlug, { limit });

  // Giới hạn số lượng phim hiển thị
  const displayMovies = movies.slice(0, limit);

  if (loading) {
    return (
      <section className={`py-6 ${className}`}>
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-2xl font-bold text-white">{title}</h2>
        </div>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
          {[...Array(limit)].map((_, i) => (
            <div key={i} className="bg-gray-800 rounded-lg overflow-hidden animate-pulse">
              <div className="aspect-[2/3] bg-gray-700"></div>
              <div className="p-2">
                <div className="h-4 bg-gray-700 rounded mb-2"></div>
                <div className="h-3 bg-gray-700 rounded w-2/3"></div>
              </div>
            </div>
          ))}
        </div>
      </section>
    );
  }

  if (error) {
    return (
      <section className={`py-6 ${className}`}>
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-2xl font-bold text-white">{title}</h2>
        </div>
        <div className="text-center py-10">
          <div className="text-red-500 text-5xl mb-4">
            <svg className="w-12 h-12 mx-auto" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          </div>
          <h3 className="text-xl font-semibold text-white mb-2">Không thể tải phim {title.toLowerCase()}</h3>
          <p className="text-gray-400 mb-4">Vui lòng kiểm tra kết nối mạng và thử lại</p>
          <button 
            onClick={refetch} 
            className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition"
          >
            Thử lại
          </button>
        </div>
      </section>
    );
  }

  if (displayMovies.length === 0) {
    return (
      <section className={`py-6 ${className}`}>
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-2xl font-bold text-white">{title}</h2>
        </div>
        <div className="text-center py-10">
          <div className="text-gray-500 text-5xl mb-4">
            <svg className="w-12 h-12 mx-auto" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
            </svg>
          </div>
          <h3 className="text-xl font-semibold text-white mb-2">Không có phim {title.toLowerCase()}</h3>
          <p className="text-gray-400">Hiện tại chưa có phim trong thể loại này</p>
        </div>
      </section>
    );
  }

  return (
    <section className={`py-6 ${className}`}>
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-2xl font-bold text-white">{title}</h2>
        {showViewAll && (
          <button className="text-gray-400 hover:text-white transition">
            Xem tất cả &rarr;
          </button>
        )}
      </div>
      
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
        {displayMovies.map((movie) => (
          <MovieCard key={movie._id} movie={movie} />
        ))}
      </div>
    </section>
  );
};

export default CategoryMovieList;
