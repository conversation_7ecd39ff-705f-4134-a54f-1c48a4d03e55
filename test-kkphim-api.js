// Test script để kiểm tra KKPhim API
const API_BASE = 'https://phimapi.com';

async function testAPI() {
  console.log('🚀 Testing KKPhim API...\n');

  // Test 1: <PERSON><PERSON><PERSON> phim mới cập nhật
  try {
    console.log('📋 Test 1: L<PERSON><PERSON> phim mới cập nhật');
    const response = await fetch(`${API_BASE}/danh-sach/phim-moi-cap-nhat?page=1`);
    const data = await response.json();

    if (data && data.items && data.items.length > 0) {
      console.log('✅ Success! Found', data.items.length, 'movies');
      console.log('📄 Sample movie:', {
        name: data.items[0].name,
        slug: data.items[0].slug,
        year: data.items[0].year,
        poster: data.items[0].poster_url
      });
    } else {
      console.log('❌ No movies found');
    }
  } catch (error) {
    console.log('❌ Error:', error.message);
  }

  console.log('\n' + '='.repeat(50) + '\n');

  // Test 2: <PERSON><PERSON><PERSON> chi tiết phim
  try {
    console.log('🎬 Test 2: <PERSON><PERSON><PERSON> chi tiết phim (ngoi-truong-xac-song)');
    const response = await fetch(`${API_BASE}/phim/ngoi-truong-xac-song`);
    const data = await response.json();

    if (data && data.movie) {
      console.log('✅ Success! Movie details:');
      console.log('📄 Movie info:', {
        name: data.movie.name,
        origin_name: data.movie.origin_name,
        year: data.movie.year,
        type: data.movie.type,
        status: data.movie.status,
        episodes: data.movie.episode_current + '/' + data.movie.episode_total
      });

      if (data.episodes && data.episodes.length > 0) {
        console.log('📺 Episodes found:', data.episodes.length, 'servers');
        console.log('🎥 First server:', data.episodes[0].server_name, 'with', data.episodes[0].server_data.length, 'episodes');
      }
    } else {
      console.log('❌ No movie details found');
    }
  } catch (error) {
    console.log('❌ Error:', error.message);
  }

  console.log('\n' + '='.repeat(50) + '\n');

  // Test 3: Tìm kiếm phim
  try {
    console.log('🔍 Test 3: Tìm kiếm phim (keyword: "one piece")');
    const response = await fetch(`${API_BASE}/v1/api/tim-kiem?keyword=one piece&page=1`);
    const data = await response.json();

    if (data && data.items && data.items.length > 0) {
      console.log('✅ Success! Found', data.items.length, 'search results');
      console.log('📄 First result:', {
        name: data.items[0].name,
        slug: data.items[0].slug,
        year: data.items[0].year
      });
    } else {
      console.log('❌ No search results found');
    }
  } catch (error) {
    console.log('❌ Error:', error.message);
  }

  console.log('\n' + '='.repeat(50) + '\n');

  // Test 4: Lấy thể loại
  try {
    console.log('🏷️ Test 4: Lấy danh sách thể loại');
    const response = await fetch(`${API_BASE}/the-loai`);
    const data = await response.json();

    if (data && Array.isArray(data) && data.length > 0) {
      console.log('✅ Success! Found', data.length, 'categories');
      console.log('📄 All categories:');
      data.forEach((cat, index) => {
        console.log(`   ${index + 1}. ${cat.name} (${cat.slug})`);
      });
    } else {
      console.log('❌ No categories found');
    }
  } catch (error) {
    console.log('❌ Error:', error.message);
  }

  console.log('\n' + '='.repeat(50) + '\n');

  // Test 5: Lấy phim theo thể loại
  try {
    console.log('🎭 Test 5: Lấy phim hành động');
    const response = await fetch(`${API_BASE}/v1/api/the-loai/hanh-dong?page=1&limit=5`);
    const data = await response.json();

    if (data && data.items && data.items.length > 0) {
      console.log('✅ Success! Found', data.items.length, 'action movies');
      console.log('📄 Sample movies:', data.items.slice(0, 3).map(movie => movie.name));
    } else {
      console.log('❌ No action movies found');
    }
  } catch (error) {
    console.log('❌ Error:', error.message);
  }

  console.log('\n🎉 API Testing completed!');
}

// Chạy test
testAPI().catch(console.error);
