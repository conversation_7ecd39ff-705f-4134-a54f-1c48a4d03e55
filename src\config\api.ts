export const API_CONFIG = {
  BASE_URL: import.meta.env.VITE_KKPHIM_API_BASE_URL || 'https://phimapi.com',
  WEBSITE_URL: import.meta.env.VITE_KKPHIM_WEBSITE_URL || 'https://kkphim.com',
  ENDPOINTS: {
    // Danh sách phim
    MOVIES_NEW_UPDATE: '/danh-sach/phim-moi-cap-nhat',
    MOVIES_NEW_UPDATE_V2: '/danh-sach/phim-moi-cap-nhat-v2',
    MOVIES_NEW_UPDATE_V3: '/danh-sach/phim-moi-cap-nhat-v3',
    
    // Chi tiết phim
    MOVIE_DETAIL: '/phim',
    
    // Danh sách tổng hợp
    MOVIE_LIST: '/v1/api/danh-sach',
    
    // Tìm kiếm
    SEARCH: '/v1/api/tim-kiem',
    
    // Thể loại
    CATEGORIES: '/the-loai',
    CATEGORY_DETAIL: '/v1/api/the-loai',
    
    // Quốc gia
    COUNTRIES: '/quoc-gia',
    COUNTRY_DETAIL: '/v1/api/quoc-gia',
    
    // Năm
    YEAR_DETAIL: '/v1/api/nam',
  },
  
  // Các loại phim
  MOVIE_TYPES: {
    PHIM_BO: 'phim-bo',
    PHIM_LE: 'phim-le',
    TV_SHOWS: 'tv-shows',
    HOAT_HINH: 'hoat-hinh',
    PHIM_VIETSUB: 'phim-vietsub',
    PHIM_THUYET_MINH: 'phim-thuyet-minh',
    PHIM_LONG_TIENG: 'phim-long-tieng',
  },
  
  // Sắp xếp
  SORT_FIELDS: {
    MODIFIED_TIME: 'modified.time',
    ID: '_id',
    YEAR: 'year',
  },
  
  SORT_TYPES: {
    ASC: 'asc',
    DESC: 'desc',
  },
  
  // Ngôn ngữ
  LANGUAGES: {
    VIETSUB: 'vietsub',
    THUYET_MINH: 'thuyet-minh',
    LONG_TIENG: 'long-tieng',
  },
  
  // Giới hạn
  LIMITS: {
    DEFAULT: 20,
    MAX: 64,
  },
} as const;

export type MovieType = typeof API_CONFIG.MOVIE_TYPES[keyof typeof API_CONFIG.MOVIE_TYPES];
export type SortField = typeof API_CONFIG.SORT_FIELDS[keyof typeof API_CONFIG.SORT_FIELDS];
export type SortType = typeof API_CONFIG.SORT_TYPES[keyof typeof API_CONFIG.SORT_TYPES];
export type Language = typeof API_CONFIG.LANGUAGES[keyof typeof API_CONFIG.LANGUAGES];
