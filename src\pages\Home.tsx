import React, { Suspense } from 'react';
import { API_CONFIG } from '../config/api';
import MovieList from '../components/MovieList';
import CategoryMovieList from '../components/CategoryMovieList';
import EpisodeSelectorDemo from '../components/EpisodeSelectorDemo';

// Lazy load components
const Header = React.lazy(() => import('../components/Header'));
const Hero = React.lazy(() => import('../components/Hero'));
const Footer = React.lazy(() => import('../components/Footer'));

// Loading component
const LoadingSection = () => (
  <div className="py-6">
    <div className="h-8 bg-gray-800 rounded w-64 mb-4 animate-pulse"></div>
    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
      {[...Array(10)].map((_, i) => (
        <div key={i} className="bg-gray-800 rounded-lg overflow-hidden animate-pulse">
          <div className="aspect-[2/3] bg-gray-700"></div>
          <div className="p-2">
            <div className="h-4 bg-gray-700 rounded mb-2"></div>
            <div className="h-3 bg-gray-700 rounded w-2/3"></div>
          </div>
        </div>
      ))}
    </div>
  </div>
);

const Home: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-900">
      <Suspense fallback={<div className="text-white text-center p-8">Đang tải header...</div>}>
        <Header />
      </Suspense>

      <Suspense fallback={<div className="text-white text-center p-8">Đang tải hero...</div>}>
        <Hero />
      </Suspense>

      <main className="px-4 md:px-8 lg:px-16 space-y-12 pb-16">
        {/* Phim mới cập nhật */}
        <Suspense fallback={<LoadingSection />}>
          <MovieList
            title="Phim Mới Cập Nhật"
            type="new"
            limit={10}
          />
        </Suspense>

        {/* Phim bộ */}
        <Suspense fallback={<LoadingSection />}>
          <MovieList
            title="Phim Bộ"
            type="by-type"
            movieType={API_CONFIG.MOVIE_TYPES.PHIM_BO}
            limit={10}
          />
        </Suspense>

        {/* Phim lẻ */}
        <Suspense fallback={<LoadingSection />}>
          <MovieList
            title="Phim Lẻ"
            type="by-type"
            movieType={API_CONFIG.MOVIE_TYPES.PHIM_LE}
            limit={10}
          />
        </Suspense>

        {/* TV Shows */}
        <Suspense fallback={<LoadingSection />}>
          <MovieList
            title="TV Shows"
            type="by-type"
            movieType={API_CONFIG.MOVIE_TYPES.TV_SHOWS}
            limit={10}
          />
        </Suspense>

        {/* Hoạt hình */}
        <Suspense fallback={<LoadingSection />}>
          <MovieList
            title="Hoạt Hình"
            type="by-type"
            movieType={API_CONFIG.MOVIE_TYPES.HOAT_HINH}
            limit={10}
          />
        </Suspense>

        {/* Phim Hành Động */}
        <Suspense fallback={<LoadingSection />}>
          <CategoryMovieList
            title="Phim Hành Động"
            categorySlug="hanh-dong"
            limit={10}
          />
        </Suspense>

        {/* Phim Hài Hước */}
        <Suspense fallback={<LoadingSection />}>
          <CategoryMovieList
            title="Phim Hài Hước"
            categorySlug="hai-huoc"
            limit={10}
          />
        </Suspense>

        {/* Phim Tình Cảm */}
        <Suspense fallback={<LoadingSection />}>
          <CategoryMovieList
            title="Phim Tình Cảm"
            categorySlug="tinh-cam"
            limit={10}
          />
        </Suspense>

        {/* Phim Kinh Dị */}
        <Suspense fallback={<LoadingSection />}>
          <CategoryMovieList
            title="Phim Kinh Dị"
            categorySlug="kinh-di"
            limit={10}
          />
        </Suspense>

        {/* Phim Viễn Tưởng */}
        <Suspense fallback={<LoadingSection />}>
          <CategoryMovieList
            title="Phim Viễn Tưởng"
            categorySlug="vien-tuong"
            limit={10}
          />
        </Suspense>

        {/* Phim Cổ Trang */}
        <Suspense fallback={<LoadingSection />}>
          <CategoryMovieList
            title="Phim Cổ Trang"
            categorySlug="co-trang"
            limit={10}
          />
        </Suspense>

        {/* Episode Selector Demo */}
        <section className="py-12">
          <h2 className="text-3xl font-bold text-white mb-8 text-center">Demo Episode Selector</h2>
          <p className="text-gray-400 text-center mb-8">
            Xem demo component chọn tập phim với dữ liệu thực từ KKPhim API
          </p>
          <Suspense fallback={<LoadingSection />}>
            <EpisodeSelectorDemo />
          </Suspense>
        </section>
      </main>

      <Suspense fallback={<div className="text-white text-center p-8">Đang tải footer...</div>}>
        <Footer />
      </Suspense>
    </div>
  );
};

export default Home;
