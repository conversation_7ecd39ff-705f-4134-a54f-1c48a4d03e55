import { useState, useEffect, useCallback } from 'react';
import { kkphimApi } from '../services/kkphimApi';
import type {
  MovieListResponse,
  MovieDetailResponse,
  SearchResponse,
  CategoryItem,
  MovieListParams,
  SearchParams,
  MovieBasic,
  MovieDetail,
} from '../types/api';
import type { MovieType } from '../config/api';

// Generic hook for API calls
function useApiCall<T>() {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const execute = useCallback(async (apiCall: () => Promise<T>) => {
    setLoading(true);
    setError(null);
    try {
      const result = await apiCall();
      setData(result);
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const reset = useCallback(() => {
    setData(null);
    setError(null);
    setLoading(false);
  }, []);

  return { data, loading, error, execute, reset };
}

// Hook for new movies
export function useNewMovies(page: number = 1, autoFetch: boolean = true) {
  const { data, loading, error, execute, reset } = useApiCall<MovieListResponse>();

  const fetchNewMovies = useCallback(() => {
    return execute(() => kkphimApi.getNewMovies(page));
  }, [execute, page]);

  useEffect(() => {
    if (autoFetch) {
      fetchNewMovies();
    }
  }, [fetchNewMovies, autoFetch]);

  return {
    movies: data?.items || [],
    pagination: data?.params?.pagination,
    loading,
    error,
    refetch: fetchNewMovies,
    reset,
  };
}

// Hook for movie detail
export function useMovieDetail(slug: string | null, autoFetch: boolean = true) {
  const { data, loading, error, execute, reset } = useApiCall<MovieDetailResponse>();

  const fetchMovieDetail = useCallback(() => {
    if (!slug) return Promise.reject(new Error('Slug is required'));
    return execute(() => kkphimApi.getMovieDetail(slug));
  }, [execute, slug]);

  useEffect(() => {
    if (autoFetch && slug) {
      fetchMovieDetail();
    }
  }, [fetchMovieDetail, autoFetch, slug]);

  return {
    movie: data?.movie,
    episodes: data?.episodes || [],
    loading,
    error,
    refetch: fetchMovieDetail,
    reset,
  };
}

// Hook for movies by type
export function useMoviesByType(type: MovieType, params: MovieListParams = {}, autoFetch: boolean = true) {
  const { data, loading, error, execute, reset } = useApiCall<MovieListResponse>();

  const fetchMovies = useCallback(() => {
    return execute(() => kkphimApi.getMoviesByType(type, params));
  }, [execute, type, params]);

  useEffect(() => {
    if (autoFetch) {
      fetchMovies();
    }
  }, [fetchMovies, autoFetch]);

  return {
    movies: data?.items || [],
    pagination: data?.params?.pagination,
    loading,
    error,
    refetch: fetchMovies,
    reset,
  };
}

// Hook for search
export function useMovieSearch() {
  const { data, loading, error, execute, reset } = useApiCall<SearchResponse>();

  const searchMovies = useCallback((searchParams: SearchParams) => {
    return execute(() => kkphimApi.searchMovies(searchParams));
  }, [execute]);

  return {
    movies: data?.items || [],
    pagination: data?.params?.pagination,
    loading,
    error,
    searchMovies,
    reset,
  };
}

// Hook for categories
export function useCategories(autoFetch: boolean = true) {
  const { data, loading, error, execute, reset } = useApiCall<CategoryItem[]>();

  const fetchCategories = useCallback(() => {
    return execute(() => kkphimApi.getCategories());
  }, [execute]);

  useEffect(() => {
    if (autoFetch) {
      fetchCategories();
    }
  }, [fetchCategories, autoFetch]);

  return {
    categories: data || [],
    loading,
    error,
    refetch: fetchCategories,
    reset,
  };
}

// Hook for countries
export function useCountries(autoFetch: boolean = true) {
  const { data, loading, error, execute, reset } = useApiCall<CategoryItem[]>();

  const fetchCountries = useCallback(() => {
    return execute(() => kkphimApi.getCountries());
  }, [execute]);

  useEffect(() => {
    if (autoFetch) {
      fetchCountries();
    }
  }, [fetchCountries, autoFetch]);

  return {
    countries: data || [],
    loading,
    error,
    refetch: fetchCountries,
    reset,
  };
}
