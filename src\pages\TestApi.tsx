import React, { useState } from 'react';
import { useNewMovies, useMovieDetail, useMovieSearch, useCategories, useCountries } from '../hooks/useKKPhimApi';
import { API_CONFIG } from '../config/api';
import MovieCard from '../components/MovieCard';

const TestApi: React.FC = () => {
  const [selectedSlug, setSelectedSlug] = useState<string>('');
  const [searchKeyword, setSearchKeyword] = useState<string>('');
  
  // Test hooks
  const { movies: newMovies, loading: loadingNew, error: errorNew } = useNewMovies(1);
  const { movie: movieDetail, episodes, loading: loadingDetail, error: errorDetail } = useMovieDetail(selectedSlug, !!selectedSlug);
  const { movies: searchResults, loading: loadingSearch, error: errorSearch, searchMovies } = useMovieSearch();
  const { categories, loading: loadingCategories, error: errorCategories } = useCategories();
  const { countries, loading: loadingCountries, error: errorCountries } = useCountries();

  const handleSearch = () => {
    if (searchKeyword.trim()) {
      searchMovies({ keyword: searchKeyword.trim(), page: 1 });
    }
  };

  const handleMovieClick = (slug: string) => {
    setSelectedSlug(slug);
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white p-6">
      <div className="max-w-7xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">KKPhim API Test</h1>
        
        {/* API Config Info */}
        <div className="bg-gray-800 p-4 rounded-lg mb-8">
          <h2 className="text-xl font-semibold mb-4">API Configuration</h2>
          <p><strong>Base URL:</strong> {API_CONFIG.BASE_URL}</p>
          <p><strong>Website URL:</strong> {API_CONFIG.WEBSITE_URL}</p>
        </div>

        {/* Search Section */}
        <div className="bg-gray-800 p-4 rounded-lg mb-8">
          <h2 className="text-xl font-semibold mb-4">Search Movies</h2>
          <div className="flex gap-4 mb-4">
            <input
              type="text"
              value={searchKeyword}
              onChange={(e) => setSearchKeyword(e.target.value)}
              placeholder="Enter movie name..."
              className="flex-1 px-4 py-2 bg-gray-700 text-white rounded-lg"
              onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
            />
            <button
              onClick={handleSearch}
              disabled={loadingSearch}
              className="px-6 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 rounded-lg"
            >
              {loadingSearch ? 'Searching...' : 'Search'}
            </button>
          </div>
          
          {errorSearch && (
            <div className="text-red-400 mb-4">Error: {errorSearch}</div>
          )}
          
          {searchResults.length > 0 && (
            <div>
              <h3 className="text-lg font-medium mb-4">Search Results ({searchResults.length})</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                {searchResults.slice(0, 12).map((movie) => (
                  <div key={movie._id} onClick={() => handleMovieClick(movie.slug)} className="cursor-pointer">
                    <MovieCard movie={movie} />
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* New Movies Section */}
        <div className="bg-gray-800 p-4 rounded-lg mb-8">
          <h2 className="text-xl font-semibold mb-4">New Movies</h2>
          {loadingNew && <div className="text-blue-400">Loading new movies...</div>}
          {errorNew && <div className="text-red-400">Error: {errorNew}</div>}
          
          {newMovies.length > 0 && (
            <div>
              <p className="mb-4">Found {newMovies.length} movies</p>
              <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                {newMovies.slice(0, 12).map((movie) => (
                  <div key={movie._id} onClick={() => handleMovieClick(movie.slug)} className="cursor-pointer">
                    <MovieCard movie={movie} />
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Categories Section */}
        <div className="bg-gray-800 p-4 rounded-lg mb-8">
          <h2 className="text-xl font-semibold mb-4">Categories</h2>
          {loadingCategories && <div className="text-blue-400">Loading categories...</div>}
          {errorCategories && <div className="text-red-400">Error: {errorCategories}</div>}
          
          {categories.length > 0 && (
            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-2">
              {categories.slice(0, 18).map((category) => (
                <div key={category._id} className="bg-gray-700 px-3 py-2 rounded text-sm">
                  {category.name}
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Countries Section */}
        <div className="bg-gray-800 p-4 rounded-lg mb-8">
          <h2 className="text-xl font-semibold mb-4">Countries</h2>
          {loadingCountries && <div className="text-blue-400">Loading countries...</div>}
          {errorCountries && <div className="text-red-400">Error: {errorCountries}</div>}
          
          {countries.length > 0 && (
            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-2">
              {countries.slice(0, 18).map((country) => (
                <div key={country._id} className="bg-gray-700 px-3 py-2 rounded text-sm">
                  {country.name}
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Movie Detail Section */}
        {selectedSlug && (
          <div className="bg-gray-800 p-4 rounded-lg mb-8">
            <h2 className="text-xl font-semibold mb-4">Movie Detail: {selectedSlug}</h2>
            {loadingDetail && <div className="text-blue-400">Loading movie detail...</div>}
            {errorDetail && <div className="text-red-400">Error: {errorDetail}</div>}
            
            {movieDetail && (
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <img 
                    src={movieDetail.poster_url} 
                    alt={movieDetail.name}
                    className="w-full max-w-sm rounded-lg"
                  />
                </div>
                <div>
                  <h3 className="text-2xl font-bold mb-2">{movieDetail.name}</h3>
                  <p className="text-gray-400 mb-2">{movieDetail.origin_name}</p>
                  <p className="mb-2"><strong>Year:</strong> {movieDetail.year}</p>
                  <p className="mb-2"><strong>Type:</strong> {movieDetail.type}</p>
                  <p className="mb-2"><strong>Status:</strong> {movieDetail.status}</p>
                  <p className="mb-2"><strong>Quality:</strong> {movieDetail.quality}</p>
                  <p className="mb-2"><strong>Language:</strong> {movieDetail.lang}</p>
                  <p className="mb-2"><strong>Episodes:</strong> {movieDetail.episode_current}/{movieDetail.episode_total}</p>
                  <p className="mb-2"><strong>Views:</strong> {movieDetail.view?.toLocaleString()}</p>
                  
                  {movieDetail.category.length > 0 && (
                    <div className="mb-2">
                      <strong>Categories:</strong> {movieDetail.category.map(cat => cat.name).join(', ')}
                    </div>
                  )}
                  
                  {movieDetail.country.length > 0 && (
                    <div className="mb-2">
                      <strong>Countries:</strong> {movieDetail.country.map(country => country.name).join(', ')}
                    </div>
                  )}
                  
                  {movieDetail.content && (
                    <div className="mb-4">
                      <strong>Content:</strong>
                      <div className="text-gray-300 mt-2" dangerouslySetInnerHTML={{ __html: movieDetail.content }} />
                    </div>
                  )}
                </div>
              </div>
            )}
            
            {episodes.length > 0 && (
              <div className="mt-6">
                <h4 className="text-lg font-semibold mb-4">Episodes ({episodes.length} servers)</h4>
                {episodes.map((server, serverIndex) => (
                  <div key={serverIndex} className="mb-4">
                    <h5 className="font-medium mb-2">{server.server_name}</h5>
                    <div className="grid grid-cols-4 md:grid-cols-8 lg:grid-cols-12 gap-2">
                      {server.server_data.slice(0, 24).map((episode, episodeIndex) => (
                        <div key={episodeIndex} className="bg-gray-700 px-2 py-1 rounded text-sm text-center">
                          {episode.name}
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default TestApi;
