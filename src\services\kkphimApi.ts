import { API_CONFIG, type MovieType } from '../config/api';
import type {
  MovieListResponse,
  MovieDetailResponse,
  SearchResponse,
  CategoryItem,
  MovieListParams,
  SearchParams,
} from '../types/api';

class KKPhimAPI {
  private baseURL: string;

  constructor() {
    this.baseURL = API_CONFIG.BASE_URL;
  }

  private async fetchAPI<T>(endpoint: string, params?: Record<string, any>): Promise<T> {
    const url = new URL(endpoint, this.baseURL);

    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          url.searchParams.append(key, value.toString());
        }
      });
    }

    try {
      const response = await fetch(url.toString(), {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          'Accept': 'application/json',
          'Accept-Language': 'vi-VN,vi;q=0.9,en;q=0.8',
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('API fetch error:', error);
      throw error;
    }
  }

  // Lấy danh sách phim mới cập nhật
  async getNewMovies(page: number = 1): Promise<MovieListResponse> {
    return this.fetchAPI<MovieListResponse>(API_CONFIG.ENDPOINTS.MOVIES_NEW_UPDATE, { page });
  }

  // Lấy danh sách phim mới cập nhật V2
  async getNewMoviesV2(page: number = 1): Promise<MovieListResponse> {
    return this.fetchAPI<MovieListResponse>(API_CONFIG.ENDPOINTS.MOVIES_NEW_UPDATE_V2, { page });
  }

  // Lấy danh sách phim mới cập nhật V3
  async getNewMoviesV3(page: number = 1): Promise<MovieListResponse> {
    return this.fetchAPI<MovieListResponse>(API_CONFIG.ENDPOINTS.MOVIES_NEW_UPDATE_V3, { page });
  }

  // Lấy chi tiết phim
  async getMovieDetail(slug: string): Promise<MovieDetailResponse> {
    return this.fetchAPI<MovieDetailResponse>(`${API_CONFIG.ENDPOINTS.MOVIE_DETAIL}/${slug}`);
  }

  // Lấy danh sách phim theo loại
  async getMoviesByType(type: MovieType, params: MovieListParams = {}): Promise<MovieListResponse> {
    const { page = 1, ...otherParams } = params;
    return this.fetchAPI<MovieListResponse>(
      `${API_CONFIG.ENDPOINTS.MOVIE_LIST}/${type}`,
      { page, ...otherParams }
    );
  }

  // Tìm kiếm phim
  async searchMovies(searchParams: SearchParams): Promise<SearchResponse> {
    const { keyword, page = 1, ...otherParams } = searchParams;
    return this.fetchAPI<SearchResponse>(API_CONFIG.ENDPOINTS.SEARCH, {
      keyword,
      page,
      ...otherParams,
    });
  }

  // Lấy danh sách thể loại
  async getCategories(): Promise<CategoryItem[]> {
    return this.fetchAPI<CategoryItem[]>(API_CONFIG.ENDPOINTS.CATEGORIES);
  }

  // Lấy phim theo thể loại
  async getMoviesByCategory(categorySlug: string, params: MovieListParams = {}): Promise<MovieListResponse> {
    const { page = 1, ...otherParams } = params;
    return this.fetchAPI<MovieListResponse>(
      `${API_CONFIG.ENDPOINTS.CATEGORY_DETAIL}/${categorySlug}`,
      { page, ...otherParams }
    );
  }

  // Lấy danh sách quốc gia
  async getCountries(): Promise<CategoryItem[]> {
    return this.fetchAPI<CategoryItem[]>(API_CONFIG.ENDPOINTS.COUNTRIES);
  }

  // Lấy phim theo quốc gia
  async getMoviesByCountry(countrySlug: string, params: MovieListParams = {}): Promise<MovieListResponse> {
    const { page = 1, ...otherParams } = params;
    return this.fetchAPI<MovieListResponse>(
      `${API_CONFIG.ENDPOINTS.COUNTRY_DETAIL}/${countrySlug}`,
      { page, ...otherParams }
    );
  }

  // Lấy phim theo năm
  async getMoviesByYear(year: number, params: MovieListParams = {}): Promise<MovieListResponse> {
    const { page = 1, ...otherParams } = params;
    return this.fetchAPI<MovieListResponse>(
      `${API_CONFIG.ENDPOINTS.YEAR_DETAIL}/${year}`,
      { page, ...otherParams }
    );
  }
}

// Export singleton instance
export const kkphimApi = new KKPhimAPI();
export default kkphimApi;
