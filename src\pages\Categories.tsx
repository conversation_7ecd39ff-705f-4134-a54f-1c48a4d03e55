import React, { Suspense } from 'react';
import CategorySelector from '../components/CategorySelector';

// Lazy load components
const Header = React.lazy(() => import('../components/Header'));
const Footer = React.lazy(() => import('../components/Footer'));

const Categories: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-900">
      <Suspense fallback={<div className="text-white text-center p-8">Đang tải header...</div>}>
        <Header />
      </Suspense>
      
      <main className="px-4 md:px-8 lg:px-16 py-8">
        <div className="max-w-7xl mx-auto">
          <h1 className="text-4xl font-bold text-white mb-8">Thể <PERSON>ạ<PERSON></h1>
          
          <Suspense fallback={
            <div className="py-6">
              <div className="h-8 bg-gray-800 rounded w-64 mb-4 animate-pulse"></div>
              <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-2">
                {[...Array(12)].map((_, i) => (
                  <div key={i} className="h-8 bg-gray-800 rounded animate-pulse"></div>
                ))}
              </div>
            </div>
          }>
            <CategorySelector />
          </Suspense>
        </div>
      </main>
      
      <Suspense fallback={<div className="text-white text-center p-8">Đang tải footer...</div>}>
        <Footer />
      </Suspense>
    </div>
  );
};

export default Categories;
