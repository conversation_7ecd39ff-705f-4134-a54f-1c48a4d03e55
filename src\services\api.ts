/**
 * API Service cho KKPhim
 * Dựa trên tài liệu API từ https://kkphim.com/tai-lieu-api
 * Cập nhật: sử dụng proxy Vite để tránh lỗi CORS
 */

import type {
  ApiResponse,
  MovieDetail,
  MovieType,
  SortField,
  SortType,
  SortLang,
  MovieApiOptions
} from '../types/movie'

// Cấu hình API
const API_CONFIG = {
  // Sử dụng proxy Vite để tránh CORS
  BASE_URL: '/api',
  CDN_URL: 'https://img.ophim1.com', // CDN URL cho hình ảnh
  TIMEOUT: 15000, // Timeout mặc định (15 giây)
  RETRY_ATTEMPTS: 3, // Số lần thử lại khi request thất bại
  RETRY_DELAY: 1000, // Thời gian chờ giữa các lần thử lại (ms)
  ENDPOINTS: {
    NEW_MOVIES: '/danh-sach/phim-moi-cap-nhat',
    MOVIE_DETAIL: '/phim',
    SEARCH: '/tim-kiem',
    CATEGORY: '/the-loai',
    COUNTRY: '/quoc-gia',
    YEAR: '/nam',
    LIST: '/danh-sach',
  }
}

/**
 * Class API Service để tương tác với KKPhim API
 */
class KKPhimApiService {
  private baseUrl = API_CONFIG.BASE_URL
  private defaultTimeout = API_CONFIG.TIMEOUT
  private defaultHeaders = {
    'Accept': 'application/json',
    'Content-Type': 'application/json',
  }

  /**
   * Tạo query string từ object params
   */
  private buildQueryString(params: Record<string, any>): string {
    const searchParams = new URLSearchParams()

    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        searchParams.append(key, value.toString())
      }
    })

    return searchParams.toString()
  }

  /**
   * Thực hiện request API với xử lý lỗi, timeout và retry
   */
  private async fetchWithTimeout<T>(
    url: string, 
    options: RequestInit = {}, 
    timeout: number = this.defaultTimeout,
    retryAttempts: number = API_CONFIG.RETRY_ATTEMPTS
  ): Promise<T> {
    let lastError: any = null;
    
    for (let attempt = 0; attempt < retryAttempts; attempt++) {
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), timeout)
      
      try {
        console.log(`Fetching ${url} (attempt ${attempt + 1}/${retryAttempts})`)
        
        const response = await fetch(url, {
          ...options,
          headers: {
            ...this.defaultHeaders,
            ...options.headers
          },
          signal: controller.signal,
          // Thêm credentials để đảm bảo cookies được gửi đi
          credentials: 'same-origin'
        })

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        const data = await response.json()
        return data as T
      } catch (error: any) {
        lastError = error
        
        // Log chi tiết lỗi
        if (error.name === 'AbortError') {
          console.error(`Request timeout after ${timeout}ms for URL: ${url}`)
        } else {
          console.error(`Error fetching ${url} (attempt ${attempt + 1}/${retryAttempts}):`, error)
        }
        
        // Nếu không phải lần thử cuối cùng, chờ một chút trước khi thử lại
        if (attempt < retryAttempts - 1) {
          await new Promise(resolve => setTimeout(resolve, API_CONFIG.RETRY_DELAY))
        }
      } finally {
        clearTimeout(timeoutId)
      }
    }
    
    // Nếu tất cả các lần thử đều thất bại, throw lỗi cuối cùng
    throw lastError || new Error(`Failed to fetch ${url} after ${retryAttempts} attempts`)
  }

  /**
   * Chuyển đổi URL hình ảnh
   */
  getImageUrl(url: string): string {
    if (!url) return ''
    if (url.startsWith('http')) return url
    return `${API_CONFIG.CDN_URL}/${url}`
  }

  /**
   * Lấy danh sách phim mới cập nhật
   */
  async getNewMovies(page: number = 1): Promise<ApiResponse> {
    try {
      return await this.fetchWithTimeout<ApiResponse>(
        `${this.baseUrl}${API_CONFIG.ENDPOINTS.NEW_MOVIES}?page=${page}`
      )
    } catch (error) {
      console.error(`Error fetching new movies:`, error)
      // Trả về dữ liệu trống thay vì throw lỗi
      return {
        status: false,
        msg: 'Failed to fetch new movies',
        data: {
          seoOnPage: {
            og_type: '',
            titleHead: '',
            descriptionHead: '',
            og_image: [],
            og_url: ''
          },
          breadCrumb: [],
          titlePage: '',
          items: [],
          params: {
            type_slug: '',
            filterCategory: [],
            filterCountry: [],
            filterYear: '',
            filterType: '',
            sortField: '',
            sortType: '',
            pagination: {
              totalItems: 0,
              totalItemsPerPage: 0,
              currentPage: 0,
              totalPages: 0
            }
          },
          type_list: '',
          APP_DOMAIN_FRONTEND: '',
          APP_DOMAIN_CDN_IMAGE: ''
        }
      }
    }
  }

  /**
   * Lấy danh sách phim mới cập nhật (phiên bản v2)
   */
  async getNewMoviesV2(page: number = 1): Promise<ApiResponse> {
    try {
      return await this.fetchWithTimeout<ApiResponse>(
        `${this.baseUrl}${API_CONFIG.ENDPOINTS.NEW_MOVIES}?page=${page}`
      )
    } catch (error) {
      console.error(`Error fetching new movies v2:`, error)
      // Trả về dữ liệu trống thay vì throw lỗi
      return this.getEmptyResponse('Failed to fetch new movies v2')
    }
  }

  /**
   * Lấy danh sách phim mới cập nhật (phiên bản v3)
   */
  async getNewMoviesV3(page: number = 1): Promise<ApiResponse> {
    try {
      return await this.fetchWithTimeout<ApiResponse>(
        `${this.baseUrl}${API_CONFIG.ENDPOINTS.NEW_MOVIES}-v3?page=${page}`
      )
    } catch (error) {
      console.error(`Error fetching new movies v3:`, error)
      // Trả về dữ liệu trống thay vì throw lỗi
      return this.getEmptyResponse('Failed to fetch new movies v3')
    }
  }

  /**
   * Lấy chi tiết phim
   */
  async getMovieDetail(slug: string): Promise<MovieDetail> {
    try {
      return await this.fetchWithTimeout<MovieDetail>(
        `${this.baseUrl}${API_CONFIG.ENDPOINTS.MOVIE_DETAIL}/${slug}`
      )
    } catch (error) {
      console.error(`Error fetching movie detail for slug "${slug}":`, error)
      // Trả về dữ liệu trống thay vì throw lỗi
      return {
        status: false,
        msg: `Failed to fetch movie detail for ${slug}`,
        movie: {
          _id: '',
          name: 'Movie Not Found',
          slug: slug,
          origin_name: '',
          poster_url: '',
          thumb_url: '',
          year: 0,
          content: 'Unable to load movie details. Please try again later.',
          type: '',
          status: '',
          time: '',
          episode_current: '',
          episode_total: '',
          quality: '',
          lang: '',
          notify: '',
          showtimes: '',
          trailer_url: '',
          chieurap: false,
          category: [],
          country: [],
          actor: [],
          director: [],
          tmdb: {
            type: '',
            id: '',
            season: null,
            vote_average: 0,
            vote_count: 0
          },
          imdb: {
            id: null
          },
          created: {
            time: ''
          },
          modified: {
            time: ''
          },
          episodes: []
        }
      }
    }
  }

  /**
   * Tìm kiếm phim
   */
  async searchMovies(keyword: string, options: MovieApiOptions = {}): Promise<ApiResponse> {
    try {
      const queryString = this.buildQueryString({
        keyword,
        page: options.page || 1,
        sort_field: options.sortField || 'modified.time',
        sort_type: options.sortType || 'desc',
        sort_lang: options.sortLang,
        category: options.category,
        country: options.country,
        year: options.year,
        limit: options.limit || 20
      })

      return await this.fetchWithTimeout<ApiResponse>(
        `${this.baseUrl}${API_CONFIG.ENDPOINTS.SEARCH}?${queryString}`
      )
    } catch (error) {
      console.error(`Error searching movies with keyword "${keyword}":`, error)
      // Trả về dữ liệu trống thay vì throw lỗi
      return this.getEmptyResponse(`No results found for "${keyword}"`)
    }
  }

  /**
   * Lấy danh sách phim theo thể loại
   */
  async getMoviesByCategory(category: string, options: MovieApiOptions = {}): Promise<ApiResponse> {
    try {
      const queryString = this.buildQueryString({
        page: options.page || 1,
        sort_field: options.sortField || 'modified.time',
        sort_type: options.sortType || 'desc',
        sort_lang: options.sortLang,
        country: options.country,
        year: options.year,
        limit: options.limit || 20
      })

      return await this.fetchWithTimeout<ApiResponse>(
        `${this.baseUrl}${API_CONFIG.ENDPOINTS.CATEGORY}/${category}?${queryString}`
      )
    } catch (error) {
      console.error(`Error fetching movies by category ${category}:`, error)
      // Trả về dữ liệu trống thay vì throw lỗi
      return this.getEmptyResponse(`No ${category} movies found`)
    }
  }

  /**
   * Lấy danh sách phim theo quốc gia
   */
  async getMoviesByCountry(country: string, options: MovieApiOptions = {}): Promise<ApiResponse> {
    try {
      const queryString = this.buildQueryString({
        page: options.page || 1,
        sort_field: options.sortField || 'modified.time',
        sort_type: options.sortType || 'desc',
        sort_lang: options.sortLang,
        category: options.category,
        year: options.year,
        limit: options.limit || 20
      })

      return await this.fetchWithTimeout<ApiResponse>(
        `${this.baseUrl}${API_CONFIG.ENDPOINTS.COUNTRY}/${country}?${queryString}`
      )
    } catch (error) {
      console.error(`Error fetching movies by country ${country}:`, error)
      // Trả về dữ liệu trống thay vì throw lỗi
      return this.getEmptyResponse(`No movies from ${country} found`)
    }
  }

  /**
   * Lấy danh sách phim theo năm phát hành
   */
  async getMoviesByYear(year: number, options: MovieApiOptions = {}): Promise<ApiResponse> {
    try {
      const queryString = this.buildQueryString({
        page: options.page || 1,
        sort_field: options.sortField || 'modified.time',
        sort_type: options.sortType || 'desc',
        sort_lang: options.sortLang,
        category: options.category,
        country: options.country,
        limit: options.limit || 20
      })

      return await this.fetchWithTimeout<ApiResponse>(
        `${this.baseUrl}${API_CONFIG.ENDPOINTS.YEAR}/${year}?${queryString}`
      )
    } catch (error) {
      console.error(`Error fetching movies by year ${year}:`, error)
      // Trả về dữ liệu trống thay vì throw lỗi
      return this.getEmptyResponse(`No movies from ${year} found`)
    }
  }

  /**
   * Lấy danh sách phim theo loại
   */
  async getMoviesByType(type: MovieType, options: MovieApiOptions = {}): Promise<ApiResponse> {
    try {
      const queryString = this.buildQueryString({
        page: options.page || 1,
        sort_field: options.sortField || 'modified.time',
        sort_type: options.sortType || 'desc',
        sort_lang: options.sortLang,
        category: options.category,
        country: options.country,
        year: options.year,
        limit: options.limit || 20
      })

      return await this.fetchWithTimeout<ApiResponse>(
        `${this.baseUrl}${API_CONFIG.ENDPOINTS.LIST}/${type}?${queryString}`
      )
    } catch (error) {
      console.error(`Error fetching ${type} movies:`, error)
      // Trả về dữ liệu trống thay vì throw lỗi
      return this.getEmptyResponse(`No ${type} movies found`)
    }
  }

  /**
   * Lấy danh sách thể loại phim
   */
  async getCategories(): Promise<any> {
    try {
      return await this.fetchWithTimeout<any>(
        `${this.baseUrl}/v1/the-loai`
      )
    } catch (error) {
      console.error('Error fetching categories:', error)
      // Trả về dữ liệu trống thay vì throw lỗi
      return { status: false, items: [] }
    }
  }

  /**
   * Lấy danh sách quốc gia sản xuất phim
   */
  async getCountries(): Promise<any> {
    try {
      return await this.fetchWithTimeout<any>(
        `${this.baseUrl}/v1/quoc-gia`
      )
    } catch (error) {
      console.error('Error fetching countries:', error)
      // Trả về dữ liệu trống thay vì throw lỗi
      return { status: false, items: [] }
    }
  }

  /**
   * Tạo đối tượng response trống để xử lý lỗi
   */
  private getEmptyResponse(message: string = 'No data available'): ApiResponse {
    return {
      status: false,
      msg: message,
      data: {
        seoOnPage: {
          og_type: '',
          titleHead: '',
          descriptionHead: '',
          og_image: [],
          og_url: ''
        },
        breadCrumb: [],
        titlePage: '',
        items: [],
        params: {
          type_slug: '',
          filterCategory: [],
          filterCountry: [],
          filterYear: '',
          filterType: '',
          sortField: '',
          sortType: '',
          pagination: {
            totalItems: 0,
            totalItemsPerPage: 0,
            currentPage: 0,
            totalPages: 0
          }
        },
        type_list: '',
        APP_DOMAIN_FRONTEND: '',
        APP_DOMAIN_CDN_IMAGE: ''
      }
    }
  }

  // Các phương thức tiện ích

  /**
   * Lấy phim hành động
   */
  async getActionMovies(limit: number = 20): Promise<ApiResponse> {
    return this.getMoviesByCategory('hanh-dong', { limit })
  }

  /**
   * Lấy phim hài hước
   */
  async getComedyMovies(limit: number = 20): Promise<ApiResponse> {
    return this.getMoviesByCategory('hai-huoc', { limit })
  }

  /**
   * Lấy phim Hàn Quốc
   */
  async getKoreanMovies(limit: number = 20): Promise<ApiResponse> {
    return this.getMoviesByCountry('han-quoc', { limit })
  }

  /**
   * Lấy phim bộ
   */
  async getSeriesMovies(limit: number = 20): Promise<ApiResponse> {
    return this.getMoviesByType('phim-bo', { limit })
  }

  /**
   * Lấy phim lẻ
   */
  async getSingleMovies(limit: number = 20): Promise<ApiResponse> {
    return this.getMoviesByType('phim-le', { limit })
  }

  /**
   * Lấy TV Shows
   */
  async getTVShows(limit: number = 20): Promise<ApiResponse> {
    return this.getMoviesByType('tv-shows', { limit })
  }

  /**
   * Lấy phim hoạt hình
   */
  async getAnimeMovies(limit: number = 20): Promise<ApiResponse> {
    return this.getMoviesByType('hoat-hinh', { limit })
  }

  /**
   * Lấy phim có Vietsub
   */
  async getVietsubMovies(limit: number = 20): Promise<ApiResponse> {
    return this.getMoviesByType('phim-moi', { 
      sortLang: 'vietsub',
      limit 
    })
  }

  /**
   * Lấy phim có Thuyết minh
   */
  async getDubbedMovies(limit: number = 20): Promise<ApiResponse> {
    return this.getMoviesByType('phim-moi', { 
      sortLang: 'thuyet-minh',
      limit 
    })
  }

  /**
   * Lấy phim có Lồng tiếng
   */
  async getVoiceOverMovies(limit: number = 20): Promise<ApiResponse> {
    return this.getMoviesByType('phim-moi', { 
      sortLang: 'long-tieng',
      limit 
    })
  }
}

// Khởi tạo và export API service
const kkPhimApi = new KKPhimApiService()
export default kkPhimApi 