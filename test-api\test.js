const fetch = require('node-fetch');

console.log('Testing API...');

fetch('https://phimapi.com/danh-sach/phim-moi-cap-nhat')
  .then(response => {
    console.log('Status:', response.status);
    return response.json();
  })
  .then(data => {
    console.log('Data received:', data ? 'Yes' : 'No');
    console.log('Sample data:', JSON.stringify(data).substring(0, 200));
  })
  .catch(error => {
    console.error('Error fetching API:', error);
  }); 